import { configureStore, combineReducers } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import createWebStorage from "redux-persist/lib/storage/createWebStorage";
import {
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import { createLogger } from "redux-logger";

// Create a noop storage for SSR
const createNoopStorage = () => {
  return {
    getItem(_key: string) {
      return Promise.resolve(null);
    },
    setItem(_key: string, value: any) {
      return Promise.resolve(value);
    },
    removeItem(_key: string) {
      return Promise.resolve();
    },
  };
};

// Use localStorage when available, otherwise use noop storage
const storage =
  typeof window !== "undefined"
    ? createWebStorage("local")
    : createNoopStorage();

// Import slices
import searchSlice from "./slices/searchSlice";
import categorySlice from "./slices/categorySlice";
import filterSlice from "./slices/filterSlice";
import productSlice from "./slices/productSlice";
import cartSlice from "./slices/cartSlice";
import orderSlice from "./slices/orderSlice";
import paymentSlice from "./slices/paymentSlice";
import userSlice from "./slices/userSlice";
import modalSlice from "./slices/modalSlice";
import navigationSlice from "./slices/navigationSlice";

// Import RTK Query API
import { apiSlice } from "./api";

// Root reducer
const rootReducer = combineReducers({
  search: searchSlice,
  category: categorySlice,
  filter: filterSlice,
  product: productSlice,
  cart: cartSlice,
  order: orderSlice,
  payment: paymentSlice,
  user: userSlice,
  modal: modalSlice,
  navigation: navigationSlice,
  // Add RTK Query API reducer
  [apiSlice.reducerPath]: apiSlice.reducer,
});

// Persist configuration
const persistConfig = {
  key: "ecommerce-root",
  storage,
  whitelist: ["cart", "user", "order", "payment"], // Persist cart, user, order, and payment data
  blacklist: ["search", "modal"], // Don't persist search and modal state
  serialize: true,
  deserialize: true,
};

// Create persisted reducer only on client side
const persistedReducer =
  typeof window !== "undefined"
    ? persistReducer(persistConfig, rootReducer)
    : rootReducer;

// Logger middleware configuration
const logger = createLogger({
  predicate: () => process.env.NODE_ENV === "development",
  collapsed: true,
  duration: true,
  timestamp: true,
  logErrors: true,
  diff: true,
});

// Configure store
export const store = configureStore({
  reducer: persistedReducer as any, // Type assertion to fix persist types
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        // Ignore these field paths in all actions
        ignoredActionsPaths: ["meta.arg", "payload.timestamp"],
        // Ignore these paths in the state
        ignoredPaths: ["items.dates"],
      },
      // Enable thunk middleware for async actions
      thunk: {
        extraArgument: {
          // Add any extra arguments you want to pass to thunks
        },
      },
    })
      // Add RTK Query middleware
      .concat(apiSlice.middleware)
      // Add logger middleware only in development
      .concat(process.env.NODE_ENV === "development" ? [logger] : []),
  devTools: process.env.NODE_ENV !== "production" && {
    name: "E-commerce App",
    trace: true,
    traceLimit: 25,
    actionSanitizer: (action: any) => ({
      ...action,
      // Sanitize sensitive data in actions
      payload:
        action.payload && typeof action.payload === "object"
          ? { ...action.payload, password: "[REDACTED]" }
          : action.payload,
    }),
    stateSanitizer: (state: any) => ({
      ...state,
      // Sanitize sensitive data in state
      user: state.user
        ? {
            ...state.user,
            currentUser: state.user.currentUser
              ? {
                  ...state.user.currentUser,
                  // Hide sensitive user data in DevTools
                  email: "[REDACTED]",
                  phoneNumber: "[REDACTED]",
                }
              : null,
          }
        : state.user,
    }),
  },
});

// Create persistor only on client side
export const persistor =
  typeof window !== "undefined" ? persistStore(store) : null;

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export store as default
export default store;
