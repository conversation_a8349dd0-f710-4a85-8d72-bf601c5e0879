"use client";

import { useEffect, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { 
  setOnlineStatus, 
  syncCartWithServer, 
  processPendingOperations,
  setOnlineStatusSync,
  setSyncInProgress
} from "@/store/slices/cartSlice";

/**
 * Hook for managing cart synchronization between online/offline states
 */
export function useCartSync() {
  const dispatch = useAppDispatch();
  const { 
    isOnline, 
    pendingOperations, 
    syncInProgress, 
    lastSyncAt 
  } = useAppSelector((state) => state.cart);

  // Handle online/offline status changes
  const handleOnlineStatusChange = useCallback(() => {
    const online = navigator.onLine;
    dispatch(setOnlineStatusSync(online));
    
    if (online) {
      // When coming back online, sync with server
      dispatch(setOnlineStatus(online));
    }
  }, [dispatch]);

  // Manual sync function
  const syncCart = useCallback(async () => {
    if (!isOnline) {
      console.warn("Cannot sync cart while offline");
      return false;
    }

    try {
      await dispatch(syncCartWithServer()).unwrap();
      return true;
    } catch (error) {
      console.error("Failed to sync cart:", error);
      return false;
    }
  }, [dispatch, isOnline]);

  // Process pending operations
  const processPending = useCallback(async () => {
    if (!isOnline || pendingOperations.length === 0) {
      return { success: false, reason: !isOnline ? "offline" : "no-pending" };
    }

    try {
      const result = await dispatch(processPendingOperations()).unwrap();
      return { success: true, ...result };
    } catch (error) {
      console.error("Failed to process pending operations:", error);
      return { success: false, error };
    }
  }, [dispatch, isOnline, pendingOperations.length]);

  // Auto-sync when coming back online
  useEffect(() => {
    const handleOnline = () => {
      handleOnlineStatusChange();
    };

    const handleOffline = () => {
      handleOnlineStatusChange();
    };

    // Listen for online/offline events
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    // Set initial status
    handleOnlineStatusChange();

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [handleOnlineStatusChange]);

  // Auto-sync periodically when online and there are pending operations
  useEffect(() => {
    if (!isOnline || pendingOperations.length === 0 || syncInProgress) {
      return;
    }

    const syncInterval = setInterval(() => {
      processPending();
    }, 30000); // Try to sync every 30 seconds

    return () => clearInterval(syncInterval);
  }, [isOnline, pendingOperations.length, syncInProgress, processPending]);

  // Auto-sync after a period of inactivity
  useEffect(() => {
    if (!isOnline || !lastSyncAt) {
      return;
    }

    const lastSync = new Date(lastSyncAt);
    const now = new Date();
    const timeSinceLastSync = now.getTime() - lastSync.getTime();
    const fiveMinutes = 5 * 60 * 1000;

    if (timeSinceLastSync > fiveMinutes) {
      syncCart();
    }
  }, [isOnline, lastSyncAt, syncCart]);

  return {
    isOnline,
    pendingOperations,
    syncInProgress,
    hasPendingOperations: pendingOperations.length > 0,
    syncCart,
    processPending,
    canSync: isOnline && !syncInProgress,
  };
}

/**
 * Hook for cart synchronization status display
 */
export function useCartSyncStatus() {
  const { 
    isOnline, 
    pendingOperations, 
    syncInProgress 
  } = useAppSelector((state) => state.cart);

  const getStatus = useCallback(() => {
    if (!isOnline) {
      return {
        status: "offline" as const,
        message: "You're offline. Changes will sync when you're back online.",
        color: "orange",
      };
    }

    if (syncInProgress) {
      return {
        status: "syncing" as const,
        message: "Syncing cart with server...",
        color: "blue",
      };
    }

    if (pendingOperations.length > 0) {
      return {
        status: "pending" as const,
        message: `${pendingOperations.length} changes waiting to sync`,
        color: "yellow",
      };
    }

    return {
      status: "synced" as const,
      message: "Cart is up to date",
      color: "green",
    };
  }, [isOnline, pendingOperations.length, syncInProgress]);

  return getStatus();
}
