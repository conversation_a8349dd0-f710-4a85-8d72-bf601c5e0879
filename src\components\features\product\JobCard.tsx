"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Icon } from "@iconify/react";
import { useModal } from "@/store/compatibility";

import type { Product } from "@/types/ecommerce";

// Import the ApplicationData type from JobApplyForm
interface ApplicationData {
  personalInfo: {
    fullName: FormDataEntryValue | null;
    phone: FormDataEntryValue | null;
    email: FormDataEntryValue | null;
    address: FormDataEntryValue | null;
  };
  experiences: Array<{
    id: string;
    company: string;
    position: string;
    duration: string;
  }>;
  references: Array<{
    id: string;
    name: string;
    position: string;
    contact: string;
  }>;
  documents: {
    cv: File | null;
    coverLetter: File | null;
  };
  jobTitle: string;
  companyName: string;
}

interface JobCardProps {
  product: Product;
  onViewDetails: () => void;
}

export default function JobCard({ product, onViewDetails }: JobCardProps) {
  const { openModal, closeModal } = useModal();

  // Transform product data to job-specific format
  const jobTitle = product.title;
  const company = product.seller.name;
  const location = product.location;
  const salary = `${product.currency}${product.price.toLocaleString()}`;
  const postedDate = product.postedAt;
  const description = product.description;

  // Calculate days ago
  const daysAgo = Math.floor(
    (Date.now() - new Date(postedDate).getTime()) / (1000 * 60 * 60 * 24)
  );

  // Simple seeded random for consistent results
  const seededRandom = (seed: number) => {
    const x = Math.sin(seed) * 10000;
    return x - Math.floor(x);
  };

  // Generate seed from product ID for consistency
  const seed = product.id ? parseInt(product.id.replace(/\D/g, "") || "1") : 1;

  // Mock job-specific data (in a real app, this would come from the product data)
  const jobType =
    product.subcategory === "full-time"
      ? "Full-time"
      : product.subcategory === "part-time"
      ? "Part-time"
      : "Freelance";
  const workMode = "Remote"; // Could be derived from product features
  const experienceLevel = "Senior Level"; // Could be derived from product features
  const applicantCount = Math.floor(seededRandom(seed) * 50) + 1; // Mock data

  // Mock skills (could be derived from product features or additional fields)
  const skills = ["React", "TypeScript", "Next.js", "Tailwind CSS", "GraphQL"];
  const requirements = skills.slice(0, 4); // Show first 4 as requirements

  const handleApplicationSubmit = (applicationData: ApplicationData) => {
    console.log("Job application submitted:", applicationData);
    // Here you would typically send the data to your backend
    // For now, we'll just close the modal and show a success message
    closeModal();
    alert("Application submitted successfully!");
  };

  function onApplyNow(
    event: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ): void {
    event.preventDefault();
    // Open JobApplyForm in modal
    openModal(
      "JOB_APPLY_FORM",
      {
        jobTitle,
        companyName: company,
        onSubmit: handleApplicationSubmit,
      },
      {
        className: "max-w-6xl w-[95vw] max-h-[95vh]",
      }
    );
  }
  return (
    <Card className="w-full border-gray-200 shadow-sm hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
      <CardHeader className="pb-3 sm:pb-4 p-4 sm:p-6">
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-start gap-3 sm:gap-4 flex-1 min-w-0">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <Icon
                icon="lucide:building-2"
                className="w-5 h-5 sm:w-6 sm:h-6 text-white"
              />
            </div>
            <div className="flex-1 min-w-0">
              <h2 className="text-base sm:text-lg md:text-xl font-semibold text-gray-900 mb-1 line-clamp-2 leading-tight">
                {jobTitle}
              </h2>
              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-xs sm:text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Icon
                    icon="lucide:building-2"
                    className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
                  />
                  <span className="truncate">{company}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Icon
                    icon="lucide:map-pin"
                    className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
                  />
                  <span className="truncate">{location}</span>
                </div>
              </div>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full min-w-[44px] min-h-[44px] sm:min-w-[40px] sm:min-h-[40px]"
          >
            <Icon icon="lucide:bookmark" className="w-4 h-4 sm:w-5 sm:h-5" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-3 sm:space-y-4 p-4 sm:p-6">
        <div className="flex flex-wrap gap-1.5 sm:gap-2">
          <Badge
            variant="secondary"
            className="bg-green-100 hover:bg-green-100 text-xs sm:text-sm"
          >
            {jobType}
          </Badge>
          <Badge
            variant="secondary"
            className="bg-blue-100 hover:bg-blue-100 text-xs sm:text-sm"
          >
            {workMode}
          </Badge>
          <Badge
            variant="secondary"
            className="bg-purple-100 hover:bg-purple-100 text-xs sm:text-sm"
          >
            {experienceLevel}
          </Badge>
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-xs sm:text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Icon
              icon="lucide:dollar-sign"
              className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
            />
            <span className="font-medium">{salary}</span>
          </div>
          <div className="flex items-center gap-1">
            <Icon
              icon="lucide:clock"
              className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
            />
            <span>
              Posted {daysAgo} {daysAgo === 1 ? "day" : "days"} ago
            </span>
          </div>
        </div>

        <Separator />

        <div className="space-y-2 sm:space-y-3">
          <p className="text-gray-700 leading-relaxed line-clamp-3 text-sm sm:text-base">
            {description}
          </p>

          <div>
            <h4 className="font-medium text-gray-900 mb-2 text-sm sm:text-base">
              Key Requirements:
            </h4>
            <div className="flex flex-wrap gap-1.5 sm:gap-2">
              {requirements.map((skill, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs sm:text-sm"
                >
                  {skill}
                </Badge>
              ))}
              <Badge variant="outline" className="text-xs sm:text-sm">
                3+ years exp
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0 pt-3 sm:pt-4 p-4 sm:p-6">
        <div className="flex items-center gap-2 sm:gap-3">
          <div className="flex -space-x-1.5 sm:-space-x-2">
            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full border-2 border-white"></div>
            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full border-2 border-white"></div>
            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-green-400 to-green-500 rounded-full border-2 border-white"></div>
          </div>
          <span className="text-xs sm:text-sm text-gray-500">
            {applicantCount} applicants
          </span>
        </div>

        <div className="flex gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={onViewDetails}
            className="flex-1 sm:flex-none text-xs sm:text-sm min-h-[44px] px-4 py-2"
          >
            View Details
          </Button>
          <Button
            size="sm"
            className="bg-[#478085] hover:bg-[#356267] text-white flex-1 sm:flex-none text-xs sm:text-sm min-h-[44px] px-4 py-2"
            onClick={onApplyNow}
          >
            Apply Now
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
