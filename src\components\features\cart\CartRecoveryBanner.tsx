"use client";

import React from "react";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { useCartRecovery } from "@/hooks/use-cart-recovery";
import { useCartSync } from "@/hooks/use-cart-sync";

/**
 * Banner component that appears when cart recovery data is available
 */
export function CartRecoveryBanner() {
  const { 
    hasRecoveryData, 
    isRecovering, 
    recoverCart, 
    clearRecoveryData, 
    getRecoveryStats 
  } = useCartRecovery();
  
  const { isOnline, hasPendingOperations } = useCartSync();
  
  // Don't show banner if no recovery data
  if (!hasRecoveryData && !hasPendingOperations) {
    return null;
  }

  const stats = getRecoveryStats();

  const handleRecover = async () => {
    await recoverCart();
  };

  const handleDismiss = () => {
    clearRecoveryData();
  };

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 mb-4 shadow-sm">
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0">
          <Icon 
            icon="lucide:shopping-cart" 
            className="w-6 h-6 text-blue-600 mt-0.5" 
          />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-sm font-semibold text-blue-900">
              Cart Recovery Available
            </h3>
            {!isOnline && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                <Icon icon="lucide:wifi-off" className="w-3 h-3 mr-1" />
                Offline
              </span>
            )}
          </div>
          
          <div className="text-sm text-blue-800 mb-3">
            {stats.itemCount > 0 && (
              <p className="mb-1">
                We found {stats.itemCount} item{stats.itemCount !== 1 ? 's' : ''} from your previous session.
              </p>
            )}
            {stats.pendingOpsCount > 0 && (
              <p className="mb-1">
                {stats.pendingOpsCount} cart operation{stats.pendingOpsCount !== 1 ? 's' : ''} are waiting to sync.
              </p>
            )}
            {!isOnline && (
              <p className="text-orange-700">
                You're currently offline. Recovery will work with local data only.
              </p>
            )}
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              onClick={handleRecover}
              disabled={isRecovering}
              className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1.5"
            >
              {isRecovering ? (
                <>
                  <Icon icon="lucide:loader-2" className="w-3 h-3 mr-1 animate-spin" />
                  Recovering...
                </>
              ) : (
                <>
                  <Icon icon="lucide:refresh-cw" className="w-3 h-3 mr-1" />
                  Recover Cart
                </>
              )}
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={handleDismiss}
              disabled={isRecovering}
              className="text-blue-700 border-blue-300 hover:bg-blue-50 text-xs px-3 py-1.5"
            >
              <Icon icon="lucide:x" className="w-3 h-3 mr-1" />
              Dismiss
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Compact cart sync status indicator
 */
export function CartSyncStatus() {
  const { isOnline, pendingOperations, syncInProgress } = useCartSync();
  
  if (isOnline && pendingOperations.length === 0 && !syncInProgress) {
    return null; // Everything is synced
  }

  const getStatusInfo = () => {
    if (!isOnline) {
      return {
        icon: "lucide:wifi-off",
        text: "Offline",
        color: "text-orange-600 bg-orange-50 border-orange-200",
      };
    }
    
    if (syncInProgress) {
      return {
        icon: "lucide:loader-2",
        text: "Syncing...",
        color: "text-blue-600 bg-blue-50 border-blue-200",
        animate: true,
      };
    }
    
    if (pendingOperations.length > 0) {
      return {
        icon: "lucide:clock",
        text: `${pendingOperations.length} pending`,
        color: "text-yellow-600 bg-yellow-50 border-yellow-200",
      };
    }
    
    return null;
  };

  const statusInfo = getStatusInfo();
  if (!statusInfo) return null;

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${statusInfo.color}`}>
      <Icon 
        icon={statusInfo.icon} 
        className={`w-3 h-3 mr-1 ${statusInfo.animate ? 'animate-spin' : ''}`} 
      />
      {statusInfo.text}
    </div>
  );
}

/**
 * Detailed cart recovery panel for settings or debug purposes
 */
export function CartRecoveryPanel() {
  const { 
    getRecoveryStats, 
    recoverCart, 
    clearRecoveryData, 
    isRecovering,
    isStorageAvailable 
  } = useCartRecovery();
  
  const { isOnline, syncCart, processPending, canSync } = useCartSync();
  const stats = getRecoveryStats();

  if (!isStorageAvailable) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center gap-2 text-red-800">
          <Icon icon="lucide:alert-triangle" className="w-5 h-5" />
          <span className="font-medium">Storage Not Available</span>
        </div>
        <p className="text-sm text-red-700 mt-1">
          Local storage is not available. Cart recovery features are disabled.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Cart Recovery</h3>
        <div className="flex items-center gap-2">
          <CartSyncStatus />
          {isOnline ? (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <Icon icon="lucide:wifi" className="w-3 h-3 mr-1" />
              Online
            </span>
          ) : (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
              <Icon icon="lucide:wifi-off" className="w-3 h-3 mr-1" />
              Offline
            </span>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-gray-600">Saved Items:</span>
          <span className="ml-2 font-medium">{stats.itemCount}</span>
        </div>
        <div>
          <span className="text-gray-600">Pending Operations:</span>
          <span className="ml-2 font-medium">{stats.pendingOpsCount}</span>
        </div>
        <div className="col-span-2">
          <span className="text-gray-600">Last Sync:</span>
          <span className="ml-2 font-medium">
            {stats.lastSyncTime 
              ? new Date(stats.lastSyncTime).toLocaleString()
              : 'Never'
            }
          </span>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-200">
        <Button
          size="sm"
          onClick={recoverCart}
          disabled={isRecovering || !stats.hasData}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {isRecovering ? (
            <>
              <Icon icon="lucide:loader-2" className="w-4 h-4 mr-2 animate-spin" />
              Recovering...
            </>
          ) : (
            <>
              <Icon icon="lucide:refresh-cw" className="w-4 h-4 mr-2" />
              Recover Cart
            </>
          )}
        </Button>

        <Button
          size="sm"
          variant="outline"
          onClick={syncCart}
          disabled={!canSync}
        >
          <Icon icon="lucide:sync" className="w-4 h-4 mr-2" />
          Sync Now
        </Button>

        <Button
          size="sm"
          variant="outline"
          onClick={processPending}
          disabled={!canSync || stats.pendingOpsCount === 0}
        >
          <Icon icon="lucide:upload" className="w-4 h-4 mr-2" />
          Process Pending
        </Button>

        <Button
          size="sm"
          variant="destructive"
          onClick={clearRecoveryData}
          disabled={isRecovering}
        >
          <Icon icon="lucide:trash-2" className="w-4 h-4 mr-2" />
          Clear Data
        </Button>
      </div>
    </div>
  );
}
