import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, Playfair_Display } from "next/font/google";
import "./globals.css";
import { ReduxProvider } from "@/store/ReduxProvider";
import {
  CompatibilityProvider,
  UserCompatibilityProvider,
  ModalCompatibilityProvider,
} from "@/store/compatibility";
import { MobileNavigationProvider } from "@/context/MobileNavigationContext";
import { GlobalModal } from "@/components/ui/GlobalModal";
import { ToastProvider } from "@/components/ui/toast";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const playfairDisplay = Playfair_Display({
  variable: "--font-playfair-display",
  subsets: ["latin"],
  style: ["normal", "italic"],
  weight: ["400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${playfairDisplay.variable} antialiased`}
      >
        <ReduxProvider>
          <ToastProvider>
            <UserCompatibilityProvider>
              <CompatibilityProvider>
                <MobileNavigationProvider>
                  <ModalCompatibilityProvider>
                    {children}
                    <GlobalModal />
                  </ModalCompatibilityProvider>
                </MobileNavigationProvider>
              </CompatibilityProvider>
            </UserCompatibilityProvider>
          </ToastProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
