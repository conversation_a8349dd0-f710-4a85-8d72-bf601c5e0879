"use client";

import React, { ComponentType } from "react";
import dynamic from "next/dynamic";

// Import modal components
const JobApplyForm = dynamic(
  () => import("@/components/features/product/detail/JobApplyForm"),
  {
    ssr: false,
  }
);

const AddToCartModal = dynamic(
  () => import("@/components/features/cart/AddToCartPage"),
  {
    ssr: false,
  }
);

// Define modal component props interface
export interface ModalComponentProps {
  modalData?: any;
  modalProps?: Record<string, unknown>;
  onClose?: () => void;
}

// Define modal registry type
export type ModalRegistry = {
  [key: string]: ComponentType<ModalComponentProps>;
};

// Modal registry - maps modal types to their components
export const modalRegistry: ModalRegistry = {
  JOB_APPLY_FORM: (props) => {
    const { modalData, onClose } = props;
    return (
      <JobApplyForm
        jobTitle={modalData?.jobTitle}
        companyName={modalData?.companyName}
        onSubmit={modalData?.onSubmit}
        onCancel={onClose}
      />
    );
  },

  ADD_TO_CART_MODAL: (props) => {
    const { modalData } = props;
    return (
      <AddToCartModal
        product={modalData?.product}
        quantity={modalData?.quantity}
        viewCartAction={modalData?.viewCartAction}
      />
    );
  },
};

// Helper function to get modal component by type
export const getModalComponent = (
  modalType: string
): ComponentType<ModalComponentProps> | null => {
  return modalRegistry[modalType] || null;
};

// Modal type constants for better type safety
export const MODAL_TYPES = {
  JOB_APPLY_FORM: "JOB_APPLY_FORM",
  ADD_TO_CART_MODAL: "ADD_TO_CART_MODAL",
} as const;

export type ModalType = (typeof MODAL_TYPES)[keyof typeof MODAL_TYPES];
